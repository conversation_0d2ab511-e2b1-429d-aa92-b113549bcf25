import vscode from "vscode"
// @ts-ignore: crypto-js doesn't have TypeScript declarations
import CryptoJS from "crypto-js"
import NodeRSA from "node-rsa"
import dayjs from "dayjs"
import { v4 as uuidv4 } from "uuid"
import * as os from "os"
import { exec } from "node:child_process"
import * as crypto from "crypto"
import { Package } from "../shared/package"

// ---- Crypto utility functions ----

export const debounce = (func: (...args: any[]) => unknown, timeout = 400): ((...args: unknown[]) => unknown) => {
	let timer: NodeJS.Timeout
	return (...args: unknown[]) => {
		clearTimeout(timer)
		timer = setTimeout(() => {
			func.apply(this, args)
		}, timeout)
	}
}

// 代码翻译--对应语言命令列表
export const TRANSLATE_COMMANDS = [
	{
		command: "vscode-plugin-ch-mobile.translate_code_cpp",
		tooltip: "代码翻译",
		toTranslateLang: "cpp",
	},
	{
		command: "vscode-plugin-ch-mobile.translate_code_java",
		tooltip: "代码翻译",
		toTranslateLang: "java",
	},
	{
		command: "vscode-plugin-ch-mobile.translate_code_python",
		tooltip: "代码翻译",
		toTranslateLang: "python",
	},
]

export function HMAC(string: string, key: string): string {
	return CryptoJS.HmacSHA1(string, key).toString(CryptoJS.enc.Hex)
}

export function SHA256(string: string) {
	return CryptoJS.SHA256(string).toString(CryptoJS.enc.Hex)
}

// RSA公钥
const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhxudxTewPgljUHEZHkusP7m3I+zA4/RGvuUMt6TtII/m4zwUOm/Y31zHBTmkCCt8k5vj9y+AmO0TsGmHooNQuMebakdmEWdcA5h7YAHHFbF2w5LcxIXjib08vgVpA+m3R5xPbLK+vfHe2aAX36b5nHReDNncY5vAl3U4CgIEBGPqyG67vJytRWqP+sfEdw5+m192Rf4SCGyiBzRmjiVlH3zeEBjdbOrkAnzKOVz6AHBl2q7LPLJKIzxjoAyhEp5qnDjHUFo5VZUgFwUOt83A/jbGMyzmjRoxBuvKcs9tBuorZyUwIsZN6E+rtQk2YqMPj4RkDsZ7LRmj6on8sN2rHQIDAQAB
-----END PUBLIC KEY-----`

const rsa = new NodeRSA()
rsa.importKey(publicKey, "pkcs8-public-pem")
rsa.setOptions({
	environment: "node",
	encryptionScheme: "pkcs1",
})

// 将数据分割为214*8 bits位块
function splitIntoBitChunks(data: Buffer) {
	const chunkSize = 214
	const chunks = []
	for (let i = 0; i < data.length; i += chunkSize) {
		chunks.push(data.slice(i, i + chunkSize))
	}
	return chunks
}

// 使用RSA公钥加密数据块
function encryptDataWithRsaPublicKey(data: Buffer) {
	const chunks = splitIntoBitChunks(data)
	const encryptedChunks = chunks.map((chunk) => {
		// 使用 crypto.publicEncrypt 进行加密
		return rsa.encrypt(chunk, "base64", "buffer")
	})
	return encryptedChunks
}

// 加密数据
export function RSA(data: string) {
	const bufferData = Buffer.from(data)

	// 加密数据
	const encryptedData = encryptDataWithRsaPublicKey(bufferData)
	return encryptedData
}

// aes解密
export function AESDecrypt(ciphertext: string, token: string) {
	const decrypted = CryptoJS.AES.decrypt(ciphertext, CryptoJS.enc.Utf8.parse(token), {
		mode: CryptoJS.mode.ECB,
		padding: CryptoJS.pad.Pkcs7,
	})

	return CryptoJS.enc.Utf8.stringify(decrypted).toString()
}

// aes加密
export function AESEncrypt(ciphertext: string, token: string) {
	const decrypted = CryptoJS.AES.encrypt(ciphertext, CryptoJS.enc.Utf8.parse(token), {
		mode: CryptoJS.mode.ECB,
		padding: CryptoJS.pad.Pkcs7,
	})
	return decrypted.toString()
}

// export const Server_URL = "http://ecloud.10086.cn"
// 默认的服务器URL
const DEFAULT_SERVER_URL = "https://api-wuxi-1.cmecloud.cn:8443"

/**
 * 获取湛卢服务器的baseUrl，按优先级顺序：
 * 1. VSCode配置中的 zhanlu.serverBaseUrl
 * 2. 插件配置中的 zhanluBaseUrl
 * 3. 默认的服务器URL
 * @param pluginBaseUrl 插件配置中的baseUrl（可选）
 * @returns 最终的服务器URL
 */
export function getZhanluBaseUrl(pluginBaseUrl?: string): string {
	// 优先使用VSCode配置中的serverBaseUrl
	const vscodeBaseUrl = vscode.workspace.getConfiguration("zhanlu").get<string>("serverBaseUrl")
	if (vscodeBaseUrl?.trim()) {
		return vscodeBaseUrl.trim()
	}

	// 其次使用插件配置中的baseUrl
	if (pluginBaseUrl?.trim()) {
		return pluginBaseUrl.trim()
	}

	// 最后使用默认URL
	return DEFAULT_SERVER_URL
}

// 构建fetch op网关请求url
export function buildOpUrl(urlPath: string, asl: { [key: string]: string }, baseURL?: string): string {
	const baseUrl = baseURL?.trim() || getZhanluBaseUrl()

	// 否则使用原有的逻辑
	const { AccessKey, secretKey, token } = asl
	// 用户发送请求的唯一随机数，防止重放攻击。用户在不同请求间要使用不同的随机数值
	const SignatureNonce = uuidv4()
	// 请求的时间戳, 有效时间为5分钟
	const Timestamp = dayjs().format("YYYY-MM-DDTHH:mm:ss") + "Z"
	const authorization = encodeURIComponent(RSA(`${Date.now()}:${token}`)[0])
	const requestParams = `AccessKey=${AccessKey}&SignatureMethod=HmacSHA1&SignatureNonce=${SignatureNonce}&SignatureVersion=V2.0&Timestamp=${Timestamp}&Version=2016-12-05&authorization=${authorization}`
	// 特殊字符转义, 构造签名字符串
	const StringToSign = `POST\n${urlPath.replaceAll("/", "%2F")}\n${SHA256(requestParams.replaceAll(":", "%3A"))}`
	// HMAC secretKey
	const key = `BC_SIGNATURE&${secretKey}`
	// HMAC生成Signature
	const Signature = HMAC(StringToSign, key)
	// const base_URL: string = vscode.workspace.getConfiguration('湛卢').get('base_URL') as string;
	// const base_URL: string = 'https://ecloud.10086.cn';

	// 构造最终请求url
	return `${baseUrl}${urlPath}?${requestParams}&Signature=${Signature}`
}

// 使用setTimeout和Promise模拟异步非阻塞的延迟函数调用
export function delayedAsyncCall(func: any, delay: number) {
	return new Promise((resolve) => {
		setTimeout(async () => {
			const result = await func() // 调用传入的异步函数
			resolve(result) // 在延迟后解决Promise
		}, delay)
	})
}

// 延迟调用
export async function delayedCallMultiTimes(func: any, delay: number, times: number): Promise<any> {
	let counter = 1

	let res = await delayedAsyncCall(func, delay)
	console.log(`999${counter}`, res)
	if (res) {
		return res
	}

	do {
		counter++
		res = await delayedAsyncCall(func, delay)
		console.log(`999${counter}`, res)
	} while (!res && counter < times)
	console.log(`999999`, res)
	return res
}

// 浏览器打开url
export function openUrlWithChildProcess(url: string) {
	url = url.trim().replaceAll(" ", '" "')
	let command
	switch (process.platform) {
		case "darwin": // macOS
			command = `open ${url}`
			break
		case "win32": // Windows
			command = `start ${url}`
			break
		case "linux": // Linux
			command = `xdg-open ${url}`
			break
		default:
			vscode.env.openExternal(vscode.Uri.parse(url))
			return
	}

	exec(command, (error, _stdout, _stderr) => {
		if (error) {
			vscode.env.openExternal(vscode.Uri.parse(url))
			console.error(`Error executing command: ${error}`)
			return
		}
	})
}

// 根据当前操作系统对应Ctrl、Alt键
export function getOsPlatformKeys() {
	const thePlatform = os.platform()
	let theCtrlAlt = {
		ctrl: "Ctrl",
		alt: "Alt",
	}
	if (thePlatform === "darwin") {
		theCtrlAlt = {
			ctrl: "⌘",
			alt: "⌥",
		}
	}

	return theCtrlAlt
}

// vscode插件版本号
export const plugin_version = Package.version

// ---- Zhanlu request functions ----

export interface ZhanluRequestHeaders {
	plugin_version: string
	plugin_type: string
	service_type: string
	task_id: string
	request: string
	[key: string]: string
}

export interface ZhanluRequestBody {
	model: string
	messages: any[]
	temperature: number
	stream: boolean
	stream_options?: { include_usage: boolean }
}

export function createZhanluRequest(
	requestBody: ZhanluRequestBody,
	asl: { [key: string]: string },
	headers: ZhanluRequestHeaders,
	baseURL?: string,
): Request {
	const encryptedBody = {
		data: AESEncrypt(JSON.stringify(requestBody), asl.token),
	}
	const uri: string = "/api/acepilot/zhanlu/aiDeveloper/chat"
	const requestUrl = buildOpUrl(uri, asl, baseURL)

	return new Request(requestUrl, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			...headers,
		},
		body: JSON.stringify(encryptedBody),
	})
}

// 手机验证码生成获取
const publicPhoneCodeKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnqiA2qP9BNvKw5DnVnrBVBhd+5gJDVn3mDemCfq/AN1cdaHV57hQo6R1ufp45mOkSwLaJcTE82zFKmgKoEAKwD1SR10rp0xJC7x3yvx2FbpEsiW9TeZlvJdri1BYKUMS8OP8ykjHSJoy0oMaV6e95R2rsu4DEH7JuA9+Bt0sOoLewvHx/fs1e28tH+928uUEKdLug+cv/XTKjLudpLjiSMPZU6EHFqrUhA9zmEasOMmg9Dj0j4sChBooCeCGnh/pYHJaosH5amhlSQ8FnEG0BQBrQbZ+qhRH4LYyqGYN8grDNeSnPj7vPDcwiEm++85i5AngZfEMnGWZg5jYDhO9+QIDAQAB
-----END PUBLIC KEY-----`

// RSA公钥加密，用于保护敏感数据传输
// 手机验证码生成使用， data不能太长
export function RsaPublicKey(data: string, publicKey = publicPhoneCodeKey) {
	try {
		const buffer = Buffer.from(data)
		const encrypted = crypto.publicEncrypt(
			{
				key: publicKey,
				padding: crypto.constants.RSA_PKCS1_PADDING,
			},
			buffer,
		)
		return encrypted.toString("base64")
	} catch (error) {
		console.error("RSA encryption error:", error)
		return ""
	}
}
