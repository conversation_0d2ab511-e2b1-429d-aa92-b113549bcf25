{"extension.displayName": "<PERSON><PERSON><PERSON>:AI Coding Assistant-Intelligent partner in development", "extension.description": "<PERSON><PERSON><PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation", "command.newTask.title": "Nouvelle Tâche", "command.explainCode.title": "Expliquer le Code", "command.fixCode.title": "Corriger le Code", "command.improveCode.title": "Améliorer le Code", "command.unitTest.title": "Test Unitaire", "command.codeReview.title": "Revue du Code", "command.commentCode.title": "Commenter le Code", "command.addToContext.title": "A<PERSON>ter au Contexte", "command.openInNewTab.title": "Ouv<PERSON>r dans un Nouvel Onglet", "command.focusInput.title": "Focus sur le Champ de Saisie", "command.setCustomStoragePath.title": "Définir le Chemin de Stockage Personnalisé", "command.importSettings.title": "Importer les Paramètres", "command.terminal.addToContext.title": "Ajouter le Contenu du Terminal au Contexte", "command.terminal.fixCommand.title": "<PERSON>rri<PERSON> cette <PERSON>e", "command.terminal.explainCommand.title": "Expliquer cette <PERSON>e", "command.acceptInput.title": "Accepter l'Entrée/Suggestion", "views.activitybar.title": "<PERSON><PERSON><PERSON>", "views.contextMenu.label": "<PERSON><PERSON><PERSON>", "views.terminalMenu.label": "<PERSON><PERSON><PERSON>", "views.sidebar.name": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "Serveurs MCP", "command.prompts.title": "Modes", "command.history.title": "Historique", "command.marketplace.title": "<PERSON><PERSON>", "command.roomoteAgent.title": "Agent <PERSON><PERSON>", "command.openInEditor.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans l'Éditeur", "command.settings.title": "Paramètres", "command.documentation.title": "Documentation", "command.logout.title": "Se déconnecter", "configuration.title": "<PERSON><PERSON><PERSON>", "commands.allowedCommands.description": "Commandes pouvant être exécutées automatiquement lorsque 'Toujours approuver les opérations d'exécution' est activé", "commands.deniedCommands.description": "Préfixes de commandes qui seront automatiquement refusés sans demander d'approbation. En cas de conflit avec les commandes autorisées, la correspondance de préfixe la plus longue a la priorité. Ajouter * pour refuser toutes les commandes.", "commands.commandExecutionTimeout.description": "Temps maximum en secondes pour attendre que l'exécution de la commande se termine avant expiration (0 = pas de délai, 1-600s, défaut : 0s)", "commands.commandTimeoutAllowlist.description": "Préfixes de commandes qui sont exclus du délai d'exécution des commandes. Les commandes correspondant à ces préfixes s'exécuteront sans restrictions de délai.", "settings.vsCodeLmModelSelector.description": "Paramètres pour l'API du modèle de langage VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Le fournisseur du modèle de langage (ex: copilot)", "settings.vsCodeLmModelSelector.family.description": "La famille du modèle de langage (ex: gpt-4)", "settings.developerMode.description": "Activer le mode développeur pour afficher les paramètres et fonctionnalités avancées", "settings.completion.enterprise_code_completion.description": "Compléter le code entreprise", "settings.rooCodeCloudEnabled.description": "Activer Ecloud Cloud.", "settings.completion.debounce_time.description": "<PERSON><PERSON>lai de déclenchement de la complétion de code en millisecondes (ms)", "settings.completion.completion_number.description": "Nombre de candidats générés pour la complétion de code", "settings.completion.inlineCompletion_granularity.description": "Préférence pour la granularité de la complétion", "settings.completion.inlineCompletion_granularity.singleRow": "Ligne unique", "settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Maximisation unique", "settings.completion.inlineCompletion_granularity.balanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.completion.multiple_line_Completion.description": "Méthode de complétion de code multi-lignes", "settings.completion.multiple_line_Completion.autoCompletion": "Complétion automatique", "settings.completion.multiple_line_Completion.triggerCompletion": "Complétion déclenchée", "settings.completion.multiple_line_Completion.autoCompletion.description": "Enter et Ctrl+K (Cmd+K sur Mac) peuvent déclencher la complétion multi-lignes", "settings.completion.multiple_line_Completion.triggerCompletion.description": "Seul Ctrl+K (Cmd+K sur Mac) déclenche la complétion multi-lignes, Enter ne le fait pas", "settings.completion.max_tokens_completion.description": "max_tokens pour la complétion de code", "settings.dmt.maxTokens.description": "Max - tokens pour les questions et réponses multimodales sur le Code de graphe", "settings.serverBaseUrl.description": "URL de base pour le serveur Zhanlu, par défaut est https://api-wuxi-1.cmecloud.cn:8443", "settings.customStoragePath.description": "Chemin de stockage personnalisé. Laisser vide pour utiliser l'emplacement par défaut. Prend en charge les chemins absolus (ex: 'D:\\RooCodeStorage')", "settings.enableCodeActions.description": "Activer les correctifs rapides de Roo Code.", "settings.autoImportSettingsPath.description": "Chemin d'accès à un fichier de configuration RooCode à importer automatiquement au démarrage de l'extension. Prend en charge les chemins absolus et les chemins relatifs au répertoire de base (par exemple, '~/Documents/roo-code-settings.json'). Laisser vide pour désactiver l'importation automatique.", "settings.useAgentRules.description": "Activer le chargement des fichiers AGENTS.md pour les règles spécifiques à l'agent (voir https://agent-rules.org/)"}