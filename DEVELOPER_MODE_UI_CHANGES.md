# 开发者模式UI隐藏功能实现

## 概述
根据需求，在非开发者模式下隐藏以下UI元素：
1. 模式选择器上的设置按钮
2. 插件市场按钮
3. Profile选择的编辑按钮
4. Codebase按钮（IndexingStatusBadge）
5. 设置页面中的高级功能标签页（供应商、语言、实验性功能、终端、提示词、上下文管理、浏览器）

## 修改的文件

### 核心文件

#### 1. ExtensionMessage.ts
- **位置**: `src/shared/ExtensionMessage.ts`
- **修改内容**:
  - 在`ExtensionState`类型中添加`developerMode: boolean`字段

#### 2. ExtensionStateContext.tsx
- **位置**: `webview-ui/src/context/ExtensionStateContext.tsx`
- **修改内容**:
  - 在`ExtensionStateContextType`接口中添加`developerMode: boolean`和`setDeveloperMode: (value: boolean) => void`
  - 在默认状态中设置`developerMode: false`
  - 在context value中添加`setDeveloperMode`函数

#### 3. ChatTextArea.tsx
- **位置**: `webview-ui/src/components/chat/ChatTextArea.tsx`
- **修改内容**:
  - 从`useExtensionState`中获取`developerMode`状态
  - 重构`getApiConfigOptions`函数：
    - 将原来的直接返回数组改为先构建`baseOptions`
    - 只在开发者模式下添加分隔符和编辑按钮（"settingsButtonClicked"选项）
    - 在依赖数组中添加`developerMode`
  - 在`renderNonEditModeControls`中使用条件渲染隐藏`IndexingStatusBadge`组件：`{developerMode && <IndexingStatusBadge />}`

#### 4. ModeSelector.tsx
- **位置**: `webview-ui/src/components/chat/ModeSelector.tsx`
- **修改内容**:
  - 从`useExtensionState`中获取`developerMode`状态
  - 将原来直接渲染的按钮组包装在条件渲染中：`{developerMode && <div className="flex flex-row gap-1 ml-auto mb-1">...}</div>}`
  - 隐藏的按钮包括：
    - 插件市场按钮（`codicon-extensions`）
    - 设置按钮（`codicon-settings-gear`）

#### 5. SettingsView.tsx
- **位置**: `webview-ui/src/components/settings/SettingsView.tsx`
- **修改内容**:
  - 重新启用语言设置：取消注释`Globe`图标导入和`LanguageSettings`组件导入
  - 在`sectionNames`数组中重新启用`"language"`
  - 从`cachedState`中获取`developerMode`状态
  - 重构`activeTab`初始化逻辑：使用函数形式，根据开发者模式选择默认标签页
  - 重构`sections`数组生成：
    - 改为使用`useMemo`和函数形式
    - 定义完整的`allSections`数组（包括重新启用的language）
    - 在非开发者模式下过滤掉特定sections：`["language", "experimental", "terminal", "prompts", "contextManagement", "browser", "providers"]`
  - 添加`useEffect`监听开发者模式变化，自动切换到可用的标签页
  - 重新启用Language Section的渲染：取消注释`{activeTab === "language" && ...}`

### 测试文件

#### 6. ModeSelector.spec.tsx
- **位置**: `webview-ui/src/components/chat/__tests__/ModeSelector.spec.tsx`
- **修改内容**:
  - 在`useExtensionState` mock中添加`developerMode: true`以确保测试中按钮可见

#### 7. ExtensionStateContext.spec.tsx
- **位置**: `webview-ui/src/context/__tests__/ExtensionStateContext.spec.tsx`
- **修改内容**:
  - 在测试的默认状态对象中添加`developerMode: false`字段

## 实现逻辑

### 开发者模式状态管理
1. **状态定义**: 在`ExtensionMessage.ts`中的`ExtensionState`类型添加`developerMode: boolean`字段
2. **状态提供**: 通过`ExtensionStateContext`提供，默认值为`false`
3. **状态访问**: 各组件通过`useExtensionState()`钩子获取`developerMode`状态

### UI元素隐藏策略
- **ModeSelector中的按钮**: 将整个按钮容器包装在`{developerMode && ...}`条件渲染中
- **ChatTextArea中的编辑按钮**: 重构选项构建逻辑，只在开发者模式下添加编辑相关选项
- **Codebase按钮**: 使用条件渲染`{developerMode && <IndexingStatusBadge />}`
- **设置页面标签页**: 使用`useMemo`和过滤逻辑，在非开发者模式下移除特定标签页

### 关键实现细节

#### ChatTextArea中的选项构建重构
```typescript
// 原来的实现：直接返回包含编辑选项的数组
return [...pinnedConfigs, ...unpinnedConfigs, separator, editOption]

// 新的实现：条件性构建
const baseOptions = [...pinnedConfigs, ...unpinnedConfigs]
const finalOptions = developerMode
  ? [...baseOptions, separator, editOption]
  : baseOptions
return finalOptions.map(...)
```

#### SettingsView中的标签页过滤
```typescript
// 定义隐藏的标签页集合
const hiddenSections = new Set<SectionName>([
  "language", "experimental", "terminal",
  "prompts", "contextManagement", "browser", "providers"
])

// 过滤逻辑
if (!developerMode) {
  return allSections.filter(section => !hiddenSections.has(section.id))
}
```

## 功能保持
- 所有功能逻辑保持不变，只是在UI层面进行隐藏
- 开发者模式下所有按钮和设置页面正常显示和工作
- 非开发者模式下相关功能的后端逻辑仍然存在，只是UI入口被隐藏

## 测试兼容性
- 更新了相关测试文件的mock以确保兼容性
- 测试中默认使用`developerMode: true`以保证现有测试通过
- 在`ExtensionStateContext.spec.tsx`中添加了`developerMode: false`的默认值测试

## 详细功能说明

### 隐藏的UI元素详情

#### 1. ModeSelector中的按钮
- **插件市场按钮**: `codicon-extensions`图标，点击打开模式市场
- **设置按钮**: `codicon-settings-gear`图标，点击切换到modes标签页

#### 2. ChatTextArea中的元素
- **编辑按钮**: Profile选择下拉菜单中的"编辑"选项，点击打开providers设置页面
- **Codebase按钮**: `IndexingStatusBadge`组件，显示代码库索引状态

#### 3. 设置页面标签页
**隐藏的标签页**（非开发者模式下不可见）：
1. **Providers（提供商）**: API配置和模型提供商设置
2. **Language（语言）**: 界面语言设置
3. **Experimental（实验性功能）**: 实验性功能开关
4. **Terminal（终端）**: 终端相关配置
5. **Prompts（提示词）**: 自定义提示词设置
6. **Context Management（上下文管理）**: 上下文处理相关设置
7. **Browser（浏览器）**: 浏览器集成相关设置

**保留的标签页**（非开发者模式下仍可见）：
1. **Auto Approve（自动批准）**: 自动批准设置（非开发者模式的默认标签页）
2. **Checkpoints（检查点）**: 检查点功能设置
3. **Notifications（通知）**: 通知相关设置
4. **Completion（补全）**: 代码补全相关设置
5. **About（关于）**: 版本信息和关于页面

### 默认标签页逻辑
- **开发者模式**: 默认显示`providers`标签页
- **普通用户模式**: 默认显示`autoApprove`标签页
- **动态切换**: 当开发者模式状态改变时，如果当前标签页不可用，会自动切换到对应模式的默认标签页

## 使用方法
用户可以通过VSCode设置中的`developerMode`选项来控制这些UI元素的显示：
- `true`: 显示所有按钮和设置页面（开发者模式）
- `false`: 隐藏指定的UI元素和高级设置页面（普通用户模式）

## 代码变更摘要
本次实现涉及以下主要变更：
1. **类型定义**: 在共享类型中添加`developerMode`字段
2. **状态管理**: 在Context中添加开发者模式状态和setter
3. **条件渲染**: 在多个组件中使用条件渲染隐藏UI元素
4. **逻辑重构**: 重构选项构建和标签页过滤逻辑
5. **测试更新**: 更新测试mock以保持兼容性

总计修改了7个文件，确保了功能的完整性和测试的兼容性。
