# 开发者模式激活功能测试

## 功能描述
在设置页面的"关于 湛卢"标签上，连续点击标题6次可以开启开发者模式。

## 测试步骤

1. 打开VSCode
2. 打开湛卢扩展
3. 进入设置页面
4. 找到"关于 湛卢"标签页
5. 连续快速点击"关于 湛卢"标题6次
6. 观察是否开启了开发者模式

## 预期结果

- 连续点击6次后，开发者模式应该被激活
- 设置页面应该显示更多的高级选项标签页（如：供应商、语言、实验性功能等）
- 鼠标悬停在标题上时，应该显示提示信息（如："点击 X 次开启开发者模式"）

## 实现细节

### 前端实现 (About.tsx)
- 添加了点击计数状态管理
- 实现了2秒超时重置机制
- 在已经是开发者模式时不响应点击
- 添加了鼠标悬停提示

### 后端实现 (webviewMessageHandler.ts)
- 将 `zhanlu.developerMode` 添加到允许的VSCode设置列表
- 修改了 `updateVSCodeSetting` 处理逻辑以支持 `bool` 字段

### 消息传递
- 使用 `updateVSCodeSetting` 消息类型
- 设置 `zhanlu.developerMode` 为 `true`

## 注意事项

- 功能只在非开发者模式下生效
- 需要在2秒内连续点击，否则计数会重置
- 点击计数会在标题的tooltip中显示
